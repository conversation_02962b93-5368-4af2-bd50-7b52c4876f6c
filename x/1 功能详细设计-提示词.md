# 角色
你是一名优秀的产品专家和系统架构专家，请根据自己的经验，做一个功能的详细设计

# 功能
## 项目名称：Sinoair Agent
## 项目背景
LLM 大模型可以很好的实现通过识别图片、PDF 文件内容，并以格式化的 Json 格式返回结果
项目使用的行业是国际航空货运代理，国际航空运输
业务实际场景中，系统界面对应的内容有很多来源，虽然文件格式不同，但需要的内容是有规范的如 IATA 规范；
期望的目标是：通过 LLM 识别各类单证的图片、PDF 等文件，返回 Agent 提示词中要求的 Json 格式内容
通过 puppeteer、 playwright 等技术回写到目标网页

## 系统角色
系统管理员：负责用户管理、大模型管理、回写网站管理、日志查看
航空代理员工：负责agent管理（创建、发布、调用、测试、迭代、升级）、使用agent获取结果，并回写到目标网站

## 功能
```mermaid
graph LR
    A[Sinoair Agent] --> B[系统设置]
    A --> C[Agent管理]
    A --> D[回填设置]
    A --> E[Agent使用]

    B --> B1[用户管理]
    B --> B2[日志查看]
    B --> B3[统计]

    C --> C1[Agent 维护]
    C --> C2[Prompt 调试]
    C --> C3[History 历史记录]
    
    E --> E1[web]
    E --> E2[浏览器插件]

```

# 要求

要求以markdown的方式展示
